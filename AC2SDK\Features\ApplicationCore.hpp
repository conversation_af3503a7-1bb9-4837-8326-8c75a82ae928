#pragma once

#include <Windows.h>

/**
 * @brief Core application management for the AC2 SDK
 * 
 * Handles DLL lifecycle, thread management, and coordinates
 * initialization and shutdown of all subsystems.
 */
class ApplicationCore {
public:
    /**
     * @brief Initialize the application and all subsystems
     * @return true if initialization successful, false otherwise
     */
    static bool Initialize();
    
    /**
     * @brief Shutdown the application and all subsystems
     */
    static void Shutdown();
    
    /**
     * @brief Run the main application logic
     * @return Exit code (0 for success)
     */
    static DWORD Run();
    
    /**
     * @brief Check if the application is initialized
     * @return true if initialized, false otherwise
     */
    static bool IsInitialized();

private:
    static bool s_initialized;
    
    /**
     * @brief Initialize all feature modules
     * @return true if all modules initialized successfully
     */
    static bool InitializeModules();
    
    /**
     * @brief Shutdown all feature modules
     */
    static void ShutdownModules();
    
    /**
     * @brief Execute the main data collection and export logic
     * @return true if execution successful
     */
    static bool ExecuteMainLogic();
    
    // Private constructor to prevent instantiation
    ApplicationCore() = default;
};
