#pragma once

#include "../SDK.hpp"
#include "../SDK/Basic.hpp"
#include "../SDK/Engine_classes.hpp"

using namespace SDK;

/**
 * @brief Centralized access to game data and objects
 * 
 * Provides clean interfaces for accessing engine, world, and game objects
 * while handling null checks and error conditions.
 */
class GameDataAccess {
public:
    /**
     * @brief Initialize the game data access system
     * @return true if initialization successful, false otherwise
     */
    static bool Initialize();
    
    /**
     * @brief Get the game engine instance
     * @return Pointer to UEngine or nullptr if not available
     */
    static UEngine* GetEngine();
    
    /**
     * @brief Get the current world instance
     * @return Pointer to UWorld or nullptr if not available
     */
    static UWorld* GetWorld();
    
    /**
     * @brief Get the player car avatar
     * @return Pointer to ACarAvatar or nullptr if not available
     */
    static ACarAvatar* GetPlayerCar();
    
    /**
     * @brief Get the current game mode
     * @return Pointer to AGameModeBase or nullptr if not available
     */
    static AGameModeBase* GetGameMode();
    
    /**
     * @brief Get the race game mode (cast from game mode)
     * @return Pointer to AAcRaceGameMode or nullptr if not available
     */
    static AAcRaceGameMode* GetRaceGameMode();
    
    /**
     * @brief Get all car avatars in the world
     * @param outCars Array to fill with car avatars
     * @return Number of cars found
     */
    static int32 GetAllCars(TArray<AActor*>& outCars);
    
    /**
     * @brief Get all realtime widgets
     * @param outWidgets Array to fill with widgets
     * @return Number of widgets found
     */
    static int32 GetAllRealtimeWidgets(TArray<UUserWidget*>& outWidgets);
    
    /**
     * @brief Check if the game data access system is properly initialized
     * @return true if initialized and ready, false otherwise
     */
    static bool IsReady();

private:
    static UEngine* s_engine;
    static UWorld* s_world;
    static ACarAvatar* s_playerCar;
    static bool s_initialized;
    
    // Private constructor to prevent instantiation
    GameDataAccess() = default;
    
    /**
     * @brief Refresh cached game objects
     */
    static void RefreshGameObjects();
};
