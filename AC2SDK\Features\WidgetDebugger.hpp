#pragma once

#include "../SDK.hpp"
#include "../SDK/Basic.hpp"
#include "../SDK/Engine_classes.hpp"
#include "../SDK/UMG_classes.hpp"
#include <fstream>
#include <string>

using namespace SDK;

/**
 * Widget Debugger - Helps identify and analyze UI widgets in the game
 * Provides functionality to:
 * - List all widgets by type
 * - Find widgets under mouse cursor
 * - Search for timing-related widgets
 * - Display widget hierarchy information
 */
class WidgetDebugger
{
  public:
  /**
   * Print comprehensive widget analysis to log file
   * @param world The game world
   * @param logFile Output stream for logging
   */
  static void AnalyzeAllWidgets(UWorld *world, std::ofstream &logFile);

  /**
   * Find and list all timing-related widgets
   * @param world The game world
   * @param logFile Output stream for logging
   */
  static void FindTimingWidgets(UWorld *world, std::ofstream &logFile);

  /**
   * Get widget under mouse cursor (if possible)
   * @param world The game world
   * @return Widget under cursor or nullptr
   */
  static UUserWidget *GetWidgetUnderCursor(UWorld *world);

  /**
   * Print widget hierarchy for a specific widget
   * @param widget The widget to analyze
   * @param logFile Output stream for logging
   * @param depth Current depth in hierarchy (for indentation)
   */
  static void PrintWidgetHierarchy(UUserWidget *widget, std::ofstream &logFile, int depth = 0);

  /**
   * Search for widgets containing specific text patterns
   * @param world The game world
   * @param searchTerms Array of terms to search for
   * @param logFile Output stream for logging
   */
  static void SearchWidgetsByName(UWorld *world, const TArray<FString> &searchTerms, std::ofstream &logFile);

  private:
  /**
   * Check if a widget class name contains timing-related keywords
   * @param className The class name to check
   * @return true if it's likely a timing widget
   */
  static bool IsTimingRelatedWidget(const std::string &className);

  /**
   * Get indentation string for hierarchy display
   * @param depth The depth level
   * @return Indentation string
   */
  static std::string GetIndentation(int depth);
};
