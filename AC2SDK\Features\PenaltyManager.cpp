#include "PenaltyManager.hpp"
#include "../SDK/UMG_classes.hpp"
#include "../SDK/WDG_PenaltyIndicator_classes.hpp"
#include "../SDK/UMG_classes.hpp"
#include <algorithm>

const char *PenaltyManager::GetPenaltyTypeName(EPenaltyType penaltyType)
{
  switch (penaltyType) {
    case EPenaltyType::None:
      return "None";
    case EPenaltyType::DriveThrough:
      return "Drive Through";
    case EPenaltyType::StopAndGo_10:
      return "Stop & Go 10s";
    case EPenaltyType::StopAndGo_20:
      return "Stop & Go 20s";
    case EPenaltyType::StopAndGo_30:
      return "Stop & Go 30s";
    case EPenaltyType::PostRaceTime:
      return "Post Race Time";
    case EPenaltyType::Disqualified:
      return "Disqualified";
    case EPenaltyType::RemoveBestLaptime:
      return "Remove Best Laptime";
    default:
      return "Unknown";
  }
}

PenaltyData PenaltyManager::GetPenaltyForCar(UWorld *world, int32 carNumber)
{
  PenaltyData result;
  result.carNumber = carNumber;

  if (!world) {
    return result;
  }

  // Try different extraction methods in order of preference

  // 1. Try realtime MFD widgets first (most reliable)
  result = ExtractFromRealtimeWidgets(world, carNumber);
  if (result.penaltyType != EPenaltyType::None) {
    return result;
  }

  // 2. Try penalty indicator widgets
  result = ExtractFromPenaltyIndicators(world, carNumber);

  return result;
}

TArray<PenaltyData> PenaltyManager::GetAllCarsPenalties(UWorld *world)
{
  TArray<PenaltyData> allPenalties;

  if (!world) {
    return allPenalties;
  }

  // Get all cars in the race
  TArray<AActor *> foundCars;
  UGameplayStatics::GetAllActorsOfClass(world, ACarAvatar::StaticClass(), &foundCars);

  for (AActor *actor : foundCars) {
    ACarAvatar *carAvatar = static_cast<ACarAvatar *>(actor);
    if (carAvatar) {
      FCarInfo carInfo = carAvatar->getCarInfoUI();
      if (carInfo.RaceNumber > 0) {
        PenaltyData penaltyData = GetPenaltyForCar(world, carInfo.RaceNumber);
        penaltyData.teamName = carInfo.TeamName.ToString();
        allPenalties.Add(penaltyData);
      }
    }
  }

  return allPenalties;
}

void PenaltyManager::LogAllCarsPenalties(UWorld *world, std::ofstream &logFile)
{
  if (!world) {
    return;
  }

  logFile << "====== ALL CARS PENALTY DATA ======" << std::endl;

  TArray<PenaltyData> allPenalties = GetAllCarsPenalties(world);
  int32 carsWithPenalties = 0;

  for (const PenaltyData &penalty : allPenalties) {
    logFile << "=== CAR " << penalty.carNumber << " ===" << std::endl;
    logFile << "Team Name: " << penalty.teamName << std::endl;
    logFile << "Penalty Type: " << GetPenaltyTypeName(penalty.penaltyType) << std::endl;
    logFile << "Penalty Weight: " << (int)penalty.penaltyWeight << " seconds" << std::endl;

    if (penalty.penaltyType != EPenaltyType::None) {
      carsWithPenalties++;
    }

    logFile << std::endl;
  }

  logFile << "Total cars with penalties: " << carsWithPenalties << " out of " << allPenalties.Num() << std::endl;
  logFile << std::endl;
}

bool PenaltyManager::HasAnyCarPenalty(UWorld *world, EPenaltyType penaltyType)
{
  TArray<PenaltyData> allPenalties = GetAllCarsPenalties(world);

  for (const PenaltyData &penalty : allPenalties) {
    if (penalty.penaltyType == penaltyType) {
      return true;
    }
  }

  return false;
}

int32 PenaltyManager::GetCarsWithPenaltiesCount(UWorld *world)
{
  TArray<PenaltyData> allPenalties = GetAllCarsPenalties(world);
  int32 count = 0;

  for (const PenaltyData &penalty : allPenalties) {
    if (penalty.penaltyType != EPenaltyType::None) {
      count++;
    }
  }

  return count;
}

void PenaltyManager::DebugPenaltyWidgets(UWorld *world, std::ofstream &logFile)
{
  if (!world) {
    return;
  }

  logFile << "====== PENALTY WIDGETS DEBUG ======" << std::endl;

  // Find all penalty-related widgets
  TArray<UUserWidget *> allWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &allWidgets, UUserWidget::StaticClass(), false);

  int32 penaltyIndicatorCount = 0;
  int32 realtimeItemCount = 0;

  for (UUserWidget *widget : allWidgets) {
    if (widget && widget->IsVisible()) {
      std::string className = widget->GetName();
      std::string lowerClassName = className;
      std::transform(lowerClassName.begin(), lowerClassName.end(), lowerClassName.begin(), ::tolower);

      if (lowerClassName.find("penalty") != std::string::npos) {
        logFile << "PENALTY WIDGET: " << className << std::endl;

        // Check if it's a penalty indicator
        UWDG_PenaltyIndicator_C *penaltyIndicator = static_cast<UWDG_PenaltyIndicator_C *>(widget);
        if (penaltyIndicator) {
          logFile << "  - Penalty Type: " << GetPenaltyTypeName(penaltyIndicator->PenaltyType) << std::endl;
          logFile << "  - Penalty Weight: " << (int)penaltyIndicator->PenaltyWeight << std::endl;
          penaltyIndicatorCount++;
        }

        logFile << std::endl;
      }

      // Check for realtime MFD items
      if (lowerClassName.find("realtime") != std::string::npos) {
        UWDG_HudMfdRealtimeItemBase_C *realtimeItem = static_cast<UWDG_HudMfdRealtimeItemBase_C *>(widget);
        if (realtimeItem && realtimeItem->PenaltyType != EPenaltyType::None) {
          logFile << "REALTIME ITEM WITH PENALTY: " << className << std::endl;
          logFile << "  - Car Number: " << realtimeItem->CarNumber << std::endl;
          logFile << "  - Penalty Type: " << GetPenaltyTypeName(realtimeItem->PenaltyType) << std::endl;
          logFile << "  - Penalty Weight: " << (int)realtimeItem->PenaltyWeight << std::endl;
          realtimeItemCount++;
          logFile << std::endl;
        }
      }
    }
  }

  logFile << "Found " << penaltyIndicatorCount << " Penalty Indicator widgets" << std::endl;
  logFile << "Found " << realtimeItemCount << " Realtime items with penalties" << std::endl;
  logFile << std::endl;
}

PenaltyData PenaltyManager::ExtractFromRealtimeWidgets(UWorld *world, int32 carNumber)
{
  PenaltyData result;
  result.carNumber = carNumber;

  if (!world) {
    return result;
  }

  // Find all realtime MFD widgets
  TArray<UUserWidget *> allWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &allWidgets, UWDG_HudMfdRealtimeItemBase_C::StaticClass(), false);

  for (UUserWidget *widget : allWidgets) {
    UWDG_HudMfdRealtimeItemBase_C *realtimeItem = static_cast<UWDG_HudMfdRealtimeItemBase_C *>(widget);
    if (realtimeItem && realtimeItem->CarNumber == carNumber) {
      result.penaltyType = realtimeItem->PenaltyType;
      result.penaltyWeight = realtimeItem->PenaltyWeight;
      result.penaltyTypeName = GetPenaltyTypeName(result.penaltyType);
      break;
    }
  }

  return result;
}

PenaltyData PenaltyManager::ExtractFromPenaltyIndicators(UWorld *world, int32 carNumber)
{
  PenaltyData result;
  result.carNumber = carNumber;

  if (!world) {
    return result;
  }

  // Find all penalty indicator widgets
  TArray<UUserWidget *> allWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &allWidgets, UWDG_PenaltyIndicator_C::StaticClass(), false);

  for (UUserWidget *widget : allWidgets) {
    UWDG_PenaltyIndicator_C *penaltyIndicator = static_cast<UWDG_PenaltyIndicator_C *>(widget);
    if (penaltyIndicator && penaltyIndicator->IsVisible()) {
      // Note: Penalty indicators don't directly store car numbers,
      // so this method is less reliable for specific car lookup
      result.penaltyType = penaltyIndicator->PenaltyType;
      result.penaltyWeight = penaltyIndicator->PenaltyWeight;
      result.penaltyTypeName = GetPenaltyTypeName(result.penaltyType);
      break; // Take first visible penalty indicator
    }
  }

  return result;
}
