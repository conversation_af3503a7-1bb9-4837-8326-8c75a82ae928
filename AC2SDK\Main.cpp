#include <Windows.h>

// Core application module
#include "Features/ApplicationCore.hpp"

/**
 * @brief Main thread function - simplified to use modular architecture
 *
 * This function now serves as a thin wrapper around the ApplicationCore
 * module, which handles all initialization, execution, and cleanup.
 */
DWORD MainThread(HMODULE Module)
{
  // Delegate all work to the ApplicationCore module
  return ApplicationCore::Run();
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID lpReserved)
{
  switch (reason) {
    case DLL_PROCESS_ATTACH:
      CreateThread(0, 0, (LPTHREAD_START_ROUTINE)MainThread, hModule, 0, 0);
      break;
  }

  return TRUE;
}