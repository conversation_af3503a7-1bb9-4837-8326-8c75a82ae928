#include <Windows.h>
#include <iostream>
#include <fstream>
#include <string>
#include <filesystem>

#include "SDK.hpp"
#include "SDK/Basic.hpp"
#include "SDK/Engine_classes.hpp"
#include "SDK/WDG_HudMfdRealtimeItemBase_classes.hpp"
#include "SDK/UMG_classes.hpp"

// Feature modules
#include "Features/WidgetDebugger.hpp"
#include "Features/SectorTiming.hpp"
#include "Features/PenaltyManager.hpp"
#include "Features/Weather.hpp"

using namespace SDK;

// Main thread function - now uses modular features

DWORD MainThread(HMODULE Module)
{
  // Create logs directory if it doesn't exist
  std::filesystem::create_directories("Z:\\Work\\UpWork\\AC2\\AC2SDK\\logs");

  // Open log file for appending
  std::ofstream logFile("Z:\\Work\\UpWork\\AC2\\AC2SDK\\logs\\dll.log", std::ios::out);
  if (!logFile.is_open()) {
    return 1;
  }

  logFile << std::endl;
  logFile << "=====================================" << std::endl;
  logFile << "====== AC2 ESSENTIAL RACE DATA ======" << std::endl;
  logFile << "=====================================" << std::endl;

  // Initialize feature modules
  WeatherManager::Initialize();

  /* Functions returning "static" instances */
  UEngine *engine = UEngine::GetEngine();
  UWorld *world = UWorld::GetWorld();

  // Get player car for current car data
  ACarAvatar *playerCar = nullptr;
  UBP_ACCUtils_C::ACC_GetPlayerCarAvatar(world, &playerCar);

  // Get weather information using Weather feature
  AGameModeBase *gameMode = UGameplayStatics::GetGameMode(world);
  AAcRaceGameMode *raceGameMode = static_cast<AAcRaceGameMode *>(gameMode);

  // Log weather data using the Weather feature
  WeatherManager::LogWeatherData(raceGameMode, logFile);

  // Get all cars and penalty data
  TArray<AActor *> foundCars;
  UGameplayStatics::GetAllActorsOfClass(world, ACarAvatar::StaticClass(), &foundCars);

  TArray<UUserWidget *> realtimeItems;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &realtimeItems, UWDG_HudMfdRealtimeItemBase_C::StaticClass(), false);

  // Use modular widget debugging system
  WidgetDebugger::AnalyzeAllWidgets(world, logFile);
  WidgetDebugger::FindTimingWidgets(world, logFile);

  // Use modular sector timing debugging
  SectorTiming::DebugTimingWidgets(world, logFile);

  logFile << std::endl;
  logFile << "====== ALL CARS ESSENTIAL DATA ======" << std::endl;

  for (int32 i = 0; i < foundCars.Num(); i++) {
    ACarAvatar *carAvatar = static_cast<ACarAvatar *>(foundCars[i]);
    if (carAvatar) {
      FCarInfo carInfo = carAvatar->getCarInfoUI();

      logFile << "=== CAR " << (i + 1) << " ===" << std::endl;
      logFile << "Car Number: " << carInfo.RaceNumber << std::endl;
      logFile << "Team Name: " << carInfo.TeamName.ToString() << std::endl;

      // Get penalty information using modular system
      PenaltyData penaltyData = PenaltyManager::GetPenaltyForCar(world, carInfo.RaceNumber);
      logFile << "Penalty Type: " << PenaltyManager::GetPenaltyTypeName(penaltyData.penaltyType) << std::endl;
      logFile << "Penalty Weight: " << (int)penaltyData.penaltyWeight << " seconds" << std::endl;

      // Get pitstop data from car avatar
      FPitstopMFD pitstopMFD = carAvatar->PitstopMFD;

      // Get sector timing data using modular system
      logFile << "=== TIMING DATA FOR CAR " << carInfo.RaceNumber << " ===" << std::endl;
      SectorTimingData sectorData = SectorTiming::GetSectorTimingForCar(world, carInfo.RaceNumber);
      logFile << "SectorOne: " << sectorData.sectorOne << (sectorData.isSectorOneBest ? " (BEST)" : "") << std::endl;
      logFile << "SectorTwo: " << sectorData.sectorTwo << (sectorData.isSectorTwoBest ? " (BEST)" : "") << std::endl;
      logFile << "SectorThree: " << sectorData.sectorThree << (sectorData.isSectorThreeBest ? " (BEST)" : "") << std::endl;
      logFile << "Lap Time: " << sectorData.lapTime << std::endl;
      logFile << "Delta to First: " << sectorData.deltaToFirst << std::endl;
      logFile << "Position: " << sectorData.position << std::endl;
      logFile << "Lap Count: " << sectorData.lapCount << std::endl;
      logFile << "Remaining Mandatory Pitstops: " << (int)pitstopMFD.missingMandatoryPitstops << std::endl;

      // Show tyre data only for player car
      if (carAvatar == playerCar) {
        logFile << "=== PLAYER CAR TYRE DATA ===" << std::endl;
        TArray<FTyreSet> tyreSets = carAvatar->tyreSets;
        logFile << "Tyre Sets Available: " << tyreSets.Num() << std::endl;
        logFile << "Current Tyre Set Index: " << carAvatar->currentTyreSetIndex << std::endl;

        // Get tyre suspension state for each wheel (tyresStatus equivalent)
        logFile << "Tyre Status (Core Temp/Pressure):" << std::endl;
        for (int32 wheelIndex = 0; wheelIndex < 4; wheelIndex++) {
          FTyreSuspState tyreState = carAvatar->GetTyreSuspState(wheelIndex);
          const char *wheelNames[] = {"FL", "FR", "RL", "RR"};
          logFile << "  " << wheelNames[wheelIndex] << ": " << tyreState.coreTemp << "C / " << tyreState.pressure << " PSI" << std::endl;
        }
      }
    }
  }

  // Flush and close the log file
  logFile.flush();
  logFile.close();

  return 0;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD reason, LPVOID lpReserved)
{
  switch (reason) {
    case DLL_PROCESS_ATTACH:
      CreateThread(0, 0, (LPTHREAD_START_ROUTINE)MainThread, hModule, 0, 0);
      break;
  }

  return TRUE;
}