#pragma once

#include "../SDK.hpp"
#include <fstream>
#include <string>

using namespace SDK;

/**
 * Weather Feature Module
 *
 * Handles all weather-related functionality including:
 * - Weather status extraction and monitoring
 * - Weather data collection
 * - Weather logging and reporting
 * - Weather preset type management
 * - Time of day tracking
 */
class WeatherManager
{
  public:
  /**
   * Initialize the Weather Manager
   */
  static void Initialize();

  /**
   * Extract and log current weather information
   * @param raceGameMode Pointer to the race game mode
   * @param logFile Output stream for logging
   */
  static void LogWeatherData(AAcRaceGameMode *raceGameMode, std::ofstream &logFile);

  /**
   * Get current weather status
   * @param raceGameMode Pointer to the race game mode
   * @return Current weather status structure
   */
  static FWeatherStatus GetCurrentWeatherStatus(AAcRaceGameMode *raceGameMode);

  /**
   * Get current weather data
   * @param raceGameMode Pointer to the race game mode
   * @return Current weather data structure
   */
  static FWeatherData GetCurrentWeatherData(AAcRaceGameMode *raceGameMode);

  /**
   * Get time of weekend in seconds
   * @param raceGameMode Pointer to the race game mode
   * @return Time of weekend in seconds
   */
  static float GetTimeOfWeekendSeconds(AAcRaceGameMode *raceGameMode);

  /**
   * Convert weather preset type to string
   * @param weatherType Weather preset type enum
   * @return String representation of weather type
   */
  static std::string WeatherPresetTypeToString(EWeatherPresetType weatherType);

  /**
   * Format temperature for display
   * @param temperature Temperature in Celsius
   * @return Formatted temperature string
   */
  static std::string FormatTemperature(float temperature);

  /**
   * Format wind information for display
   * @param windSpeed Wind speed in m/s
   * @param windDirection Wind direction in degrees
   * @return Formatted wind information string
   */
  static std::string FormatWindInfo(float windSpeed, float windDirection);

  private:
  static bool s_initialized;
};
