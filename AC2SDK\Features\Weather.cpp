#include "Weather.hpp"
#include <iomanip>
#include <sstream>

bool WeatherManager::s_initialized = false;

void WeatherManager::Initialize()
{
  if (s_initialized) {
    return;
  }

  s_initialized = true;
}

void WeatherManager::LogWeatherData(AAcRaceGameMode *raceGameMode, std::ofstream &logFile)
{
  if (!raceGameMode) {
    logFile << "====== CURRENT WEATHER ======" << std::endl;
    logFile << "Weather data unavailable - no race game mode" << std::endl;
    return;
  }

  try {
    FWeatherStatus weatherStatus = raceGameMode->getWeatherStatusForUI();
    float timeOfWeekendSeconds = raceGameMode->getTimeOfDayAsSecondsUI();

    logFile << std::endl;
    logFile << "====== CURRENT WEATHER ======" << std::endl;
    logFile << "Cloud Level: " << weatherStatus.CloudLevel << std::endl;
    logFile << "Rain Level: " << weatherStatus.RainLevel << std::endl;
    logFile << "Ambient Temperature: " << FormatTemperature(weatherStatus.ambientTemperature) << std::endl;
    logFile << "Road Temperature: " << FormatTemperature(weatherStatus.RoadTemperature) << std::endl;
    logFile << "Wind Speed: " << weatherStatus.windSpeed << " m/s" << std::endl;
    logFile << "Wind Direction: " << weatherStatus.windDirection << "°" << std::endl;
    logFile << "Rain in 10 Minutes: " << weatherStatus.RainIn10Minutes << std::endl;
    logFile << "Rain in 30 Minutes: " << weatherStatus.RainIn30Minutes << std::endl;
    logFile << "Time of Weekend (seconds): " << timeOfWeekendSeconds << std::endl;
  } catch (...) {
    logFile << "Error extracting weather data" << std::endl;
  }
}

FWeatherStatus WeatherManager::GetCurrentWeatherStatus(AAcRaceGameMode *raceGameMode)
{
  FWeatherStatus defaultStatus = {};
  if (!raceGameMode) {
    return defaultStatus;
  }

  try {
    return raceGameMode->getWeatherStatusForUI();
  } catch (...) {
    return defaultStatus;
  }
}

FWeatherData WeatherManager::GetCurrentWeatherData(AAcRaceGameMode *raceGameMode)
{
  FWeatherData defaultData = {};
  if (!raceGameMode) {
    return defaultData;
  }

  try {
    // Get weather data from game instance instead of race game mode
    UWorld *world = UWorld::GetWorld();
    if (!world) {
      return defaultData;
    }

    UGameInstance *gameInstance = UGameplayStatics::GetGameInstance(world);
    UAcGameInstance *acGameInstance = static_cast<UAcGameInstance *>(gameInstance);
    if (!acGameInstance) {
      return defaultData;
    }

    return acGameInstance->getWeatherDataForUI();
  } catch (...) {
    return defaultData;
  }
}

float WeatherManager::GetTimeOfWeekendSeconds(AAcRaceGameMode *raceGameMode)
{
  if (!raceGameMode) {
    return 0.0f;
  }

  try {
    return raceGameMode->getTimeOfDayAsSecondsUI();
  } catch (...) {
    return 0.0f;
  }
}

std::string WeatherManager::WeatherPresetTypeToString(EWeatherPresetType weatherType)
{
  switch (weatherType) {
    case EWeatherPresetType::Sunny:
      return "Sunny";
    case EWeatherPresetType::Cloudy:
      return "Cloudy";
    case EWeatherPresetType::LightRain:
      return "Light Rain";
    case EWeatherPresetType::MediumRain:
      return "Medium Rain";
    case EWeatherPresetType::HeavyRain:
      return "Heavy Rain";
    case EWeatherPresetType::ThunderStorm:
      return "Thunder Storm";
    case EWeatherPresetType::Random:
      return "Random";
    case EWeatherPresetType::Custom:
      return "Custom";
    default:
      return "Unknown";
  }
}

std::string WeatherManager::FormatTemperature(float temperature)
{
  std::ostringstream oss;
  oss << std::fixed << std::setprecision(1) << temperature << "°C";
  return oss.str();
}

std::string WeatherManager::FormatWindInfo(float windSpeed, float windDirection)
{
  std::ostringstream oss;
  oss << std::fixed << std::setprecision(1) << windSpeed << " m/s @ " << windDirection << "°";
  return oss.str();
}
