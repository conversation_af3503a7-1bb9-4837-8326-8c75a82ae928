#include "WidgetDebugger.hpp"
#include "../SDK/UMG_classes.hpp"
#include <map>
#include <algorithm>
#include <cctype>

void WidgetDebugger::AnalyzeAllWidgets(UWorld *world, std::ofstream &logFile)
{
  if (!world) {
    logFile << "ERROR: World is null" << std::endl;
    return;
  }

  logFile << "\n====== COMPREHENSIVE WIDGET ANALYSIS ======" << std::endl;

  // Get all widgets
  TArray<UUserWidget *> allWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &allWidgets, UUserWidget::StaticClass(), false);

  // Create maps to count and categorize widgets
  std::map<std::string, int32> widgetCounts;
  TArray<UUserWidget *> visibleWidgets;
  TArray<UUserWidget *> timingWidgets;

  for (UUserWidget *widget : allWidgets) {
    if (widget) {
      std::string className = widget->GetName();

      // Count all widgets
      widgetCounts[className]++;

      // Track visible widgets
      if (widget->IsVisible()) {
        visibleWidgets.Add(widget);

        // Check if it's timing-related
        if (IsTimingRelatedWidget(className)) {
          timingWidgets.Add(widget);
        }
      }
    }
  }

  // Print widget type summary
  logFile << "=== WIDGET TYPE SUMMARY ===" << std::endl;
  logFile << "Total widgets found: " << allWidgets.Num() << std::endl;
  logFile << "Visible widgets: " << visibleWidgets.Num() << std::endl;
  logFile << "Timing-related widgets: " << timingWidgets.Num() << std::endl;
  logFile << std::endl;

  // Print all widget types with counts
  for (const auto &pair : widgetCounts) {
    if (pair.second > 0) {
      logFile << pair.first << ": " << pair.second << " instances" << std::endl;
    }
  }

  logFile << std::endl;
}

void WidgetDebugger::FindTimingWidgets(UWorld *world, std::ofstream &logFile)
{
  if (!world) {
    logFile << "ERROR: World is null" << std::endl;
    return;
  }

  logFile << "\n=== TIMING-RELATED WIDGETS (VISIBLE ONLY) ===" << std::endl;

  TArray<UUserWidget *> allWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &allWidgets, UUserWidget::StaticClass(), false);

  int32 timingWidgetCount = 0;

  for (UUserWidget *widget : allWidgets) {
    if (widget && widget->IsVisible()) {
      std::string className = widget->GetName();

      if (IsTimingRelatedWidget(className)) {
        logFile << "TIMING WIDGET: " << className << std::endl;

        // Try to get more details about the widget
        PrintWidgetHierarchy(widget, logFile, 1);

        timingWidgetCount++;
        logFile << std::endl;
      }
    }
  }

  if (timingWidgetCount == 0) {
    logFile << "No timing-related widgets found!" << std::endl;
  }

  logFile << std::endl;
}

UUserWidget *WidgetDebugger::GetWidgetUnderCursor(UWorld *world)
{
  if (!world) {
    return nullptr;
  }

  // This is a placeholder - UE4/5 widget hit testing would require more complex implementation
  // For now, we'll return nullptr and focus on other debugging methods
  return nullptr;
}

void WidgetDebugger::PrintWidgetHierarchy(UUserWidget *widget, std::ofstream &logFile, int depth)
{
  if (!widget) {
    return;
  }

  std::string indent = GetIndentation(depth);
  std::string className = widget->GetName();

  logFile << indent << "- " << className;

  if (widget->IsVisible()) {
    logFile << " (VISIBLE)";
  } else {
    logFile << " (HIDDEN)";
  }

  logFile << std::endl;

  // Try to get child widgets if this widget has a panel or container
  // This is a simplified approach - real hierarchy would require more widget-specific logic
}

void WidgetDebugger::SearchWidgetsByName(UWorld *world, const TArray<FString> &searchTerms, std::ofstream &logFile)
{
  if (!world) {
    logFile << "ERROR: World is null" << std::endl;
    return;
  }

  logFile << "\n=== WIDGET SEARCH RESULTS ===" << std::endl;

  TArray<UUserWidget *> allWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &allWidgets, UUserWidget::StaticClass(), false);

  for (const FString &searchTerm : searchTerms) {
    logFile << "Searching for: " << searchTerm.ToString() << std::endl;

    int32 matchCount = 0;
    for (UUserWidget *widget : allWidgets) {
      if (widget) {
        std::string className = widget->GetName();
        std::string lowerClassName = className;
        std::string lowerSearchTerm = searchTerm.ToString();

        // Convert to lowercase
        std::transform(lowerClassName.begin(), lowerClassName.end(), lowerClassName.begin(), ::tolower);
        std::transform(lowerSearchTerm.begin(), lowerSearchTerm.end(), lowerSearchTerm.begin(), ::tolower);

        if (lowerClassName.find(lowerSearchTerm) != std::string::npos) {
          logFile << "  MATCH: " << className;
          if (widget->IsVisible()) {
            logFile << " (VISIBLE)";
          } else {
            logFile << " (HIDDEN)";
          }
          logFile << std::endl;
          matchCount++;
        }
      }
    }

    if (matchCount == 0) {
      logFile << "  No matches found." << std::endl;
    }

    logFile << std::endl;
  }
}

bool WidgetDebugger::IsTimingRelatedWidget(const std::string &className)
{
  std::string lowerClassName = className;
  std::transform(lowerClassName.begin(), lowerClassName.end(), lowerClassName.begin(), ::tolower);

  // Check for timing-related keywords
  return lowerClassName.find("time") != std::string::npos || lowerClassName.find("sector") != std::string::npos || lowerClassName.find("lap") != std::string::npos ||
         lowerClassName.find("table") != std::string::npos || lowerClassName.find("standing") != std::string::npos || lowerClassName.find("leader") != std::string::npos ||
         lowerClassName.find("position") != std::string::npos || lowerClassName.find("result") != std::string::npos || lowerClassName.find("timing") != std::string::npos ||
         lowerClassName.find("delta") != std::string::npos || lowerClassName.find("split") != std::string::npos;
}

std::string WidgetDebugger::GetIndentation(int depth)
{
  std::string indent = "";
  for (int i = 0; i < depth; i++) {
    indent += "  ";
  }
  return indent;
}
