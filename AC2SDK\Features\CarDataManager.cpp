#include "CarDataManager.hpp"
#include "GameDataAccess.hpp"
#include <vector>

int32 CarDataManager::CollectAllCarData(std::vector<CarData> &outCarData)
{
  TArray<AActor *> foundCars;
  int32 carCount = GameDataAccess::GetAllCars(foundCars);

  if (carCount == 0) {
    return 0;
  }

  outCarData.clear();
  outCarData.reserve(carCount);

  ACarAvatar *playerCar = GameDataAccess::GetPlayerCar();

  for (int32 i = 0; i < foundCars.Num(); i++) {
    ACarAvatar *carAvatar = static_cast<ACarAvatar *>(foundCars[i]);
    if (carAvatar) {
      bool isPlayer = (carAvatar == playerCar);
      CarData carData = CollectCarData(carAvatar, isPlayer);
      outCarData.push_back(carData);
    }
  }

  return static_cast<int32>(outCarData.size());
}

CarData CarDataManager::CollectCarData(ACarAvatar *carAvatar, bool isPlayerCar)
{
  CarData carData;

  if (!carAvatar) {
    return carData;
  }

  // Store the car avatar reference
  carData.carAvatar = carAvatar;

  // Get basic car information
  carData.carInfo = carAvatar->getCarInfoUI();

  // Get pitstop data
  carData.pitstopMFD = carAvatar->PitstopMFD;

  // Get penalty information using modular system
  UWorld *world = GameDataAccess::GetWorld();
  if (world) {
    carData.penaltyData = PenaltyManager::GetPenaltyForCar(world, carData.carInfo.RaceNumber);
    carData.sectorData = SectorTiming::GetSectorTimingForCar(world, carData.carInfo.RaceNumber);
  }

  // Collect tyre data if this is the player car
  if (isPlayerCar) {
    CollectTyreData(carAvatar, carData);
  }

  return carData;
}

CarData CarDataManager::GetPlayerCarData()
{
  ACarAvatar *playerCar = GameDataAccess::GetPlayerCar();
  if (!playerCar) {
    return CarData();
  }

  return CollectCarData(playerCar, true);
}

bool CarDataManager::IsPlayerCar(ACarAvatar *carAvatar)
{
  ACarAvatar *playerCar = GameDataAccess::GetPlayerCar();
  return (carAvatar && playerCar && carAvatar == playerCar);
}

void CarDataManager::CollectTyreData(ACarAvatar *carAvatar, CarData &carData)
{
  if (!carAvatar) {
    return;
  }

  // Get tyre sets
  carData.tyreSets = carAvatar->tyreSets;
  carData.currentTyreSetIndex = carAvatar->currentTyreSetIndex;
  carData.hasTyreData = true;

  // Get tyre status for each wheel
  for (int32 wheelIndex = 0; wheelIndex < 4; wheelIndex++) {
    FTyreSuspState tyreState = carAvatar->GetTyreSuspState(wheelIndex);
    carData.tyreStatus[wheelIndex].coreTemp = tyreState.coreTemp;
    carData.tyreStatus[wheelIndex].pressure = tyreState.pressure;
  }
}
