#pragma once

#include "../SDK/AC2_structs.hpp"
#include "../SDK/Engine_classes.hpp"
#include "../SDK/WDG_HudMfdRealtimeItemBase_classes.hpp"
#include <string>
#include <fstream>

using namespace SDK;

/**
 * Data structure to hold penalty information for a car
 */
struct PenaltyData {
    EPenaltyType penaltyType = EPenaltyType::None;
    uint8 penaltyWeight = 0;
    int32 carNumber = 0;
    std::string penaltyTypeName = "None";
    std::string teamName = "";
};

/**
 * PenaltyManager - Handles penalty data extraction and management for AC2
 * 
 * This class provides functionality to:
 * - Extract penalty information from game widgets
 * - Convert penalty types to human-readable names
 * - Log penalty data for all cars
 * - Search for specific penalty types
 */
class PenaltyManager {
public:
    /**
     * Get penalty information for a specific car
     * @param world The game world
     * @param carNumber The car's race number
     * @return PenaltyData structure with penalty information
     */
    static PenaltyData GetPenaltyForCar(UWorld* world, int32 carNumber);
    
    /**
     * Get penalty information for all cars in the race
     * @param world The game world
     * @return Array of PenaltyData for all cars
     */
    static TArray<PenaltyData> GetAllCarsPenalties(UWorld* world);
    
    /**
     * Convert penalty type enum to human-readable string
     * @param penaltyType The penalty type enum
     * @return Human-readable penalty type name
     */
    static const char* GetPenaltyTypeName(EPenaltyType penaltyType);
    
    /**
     * Log penalty information for all cars to file
     * @param world The game world
     * @param logFile Output stream for logging
     */
    static void LogAllCarsPenalties(UWorld* world, std::ofstream& logFile);
    
    /**
     * Check if any car has a specific penalty type
     * @param world The game world
     * @param penaltyType The penalty type to search for
     * @return True if any car has this penalty type
     */
    static bool HasAnyCarPenalty(UWorld* world, EPenaltyType penaltyType);
    
    /**
     * Get count of cars with penalties
     * @param world The game world
     * @return Number of cars with active penalties
     */
    static int32 GetCarsWithPenaltiesCount(UWorld* world);
    
    /**
     * Debug penalty widgets and their contents
     * @param world The game world
     * @param logFile Output stream for logging
     */
    static void DebugPenaltyWidgets(UWorld* world, std::ofstream& logFile);

private:
    /**
     * Extract penalty data from realtime MFD widgets
     * @param world The game world
     * @param carNumber The car's race number
     * @return PenaltyData extracted from widgets
     */
    static PenaltyData ExtractFromRealtimeWidgets(UWorld* world, int32 carNumber);
    
    /**
     * Extract penalty data from penalty indicator widgets
     * @param world The game world
     * @param carNumber The car's race number
     * @return PenaltyData extracted from penalty indicators
     */
    static PenaltyData ExtractFromPenaltyIndicators(UWorld* world, int32 carNumber);
};
