#pragma once

#include "../SDK.hpp"
#include "../SDK/Basic.hpp"
#include "PenaltyManager.hpp"
#include "SectorTiming.hpp"

using namespace SDK;

/**
 * @brief Comprehensive car data structure
 */
struct CarData {
    // Basic car information
    FCarInfo carInfo;
    ACarAvatar* carAvatar;
    
    // Pitstop information
    FPitstopMFD pitstopMFD;
    
    // Penalty information
    PenaltyData penaltyData;
    
    // Sector timing information
    SectorTimingData sectorData;
    
    // Tyre information (only for player car)
    TArray<FTyreSet> tyreSets;
    int32 currentTyreSetIndex;
    bool hasTyreData;
    
    // Tyre status for each wheel
    struct TyreStatus {
        float coreTemp;
        float pressure;
    };
    TyreStatus tyreStatus[4]; // FL, FR, RL, RR
    
    CarData() : carAvatar(nullptr), currentTyreSetIndex(-1), hasTyreData(false) {
        for (int i = 0; i < 4; i++) {
            tyreStatus[i] = {0.0f, 0.0f};
        }
    }
};

/**
 * @brief Manages car data collection and processing
 * 
 * Handles enumeration of all cars, extraction of car-specific data,
 * and aggregation of information from various game systems.
 */
class CarDataManager {
public:
    /**
     * @brief Collect data for all cars in the world
     * @param outCarData Vector to fill with car data
     * @return Number of cars processed
     */
    static int32 CollectAllCarData(std::vector<CarData>& outCarData);
    
    /**
     * @brief Collect data for a specific car
     * @param carAvatar The car avatar to collect data for
     * @param isPlayerCar Whether this is the player's car (affects data collection)
     * @return CarData structure with collected information
     */
    static CarData CollectCarData(ACarAvatar* carAvatar, bool isPlayerCar = false);
    
    /**
     * @brief Get the player car data specifically
     * @return CarData for player car, or empty CarData if not found
     */
    static CarData GetPlayerCarData();
    
    /**
     * @brief Check if a car avatar is the player's car
     * @param carAvatar The car avatar to check
     * @return true if this is the player's car, false otherwise
     */
    static bool IsPlayerCar(ACarAvatar* carAvatar);

private:
    /**
     * @brief Collect tyre data for a car (only works for player car)
     * @param carAvatar The car avatar
     * @param carData The car data structure to fill
     */
    static void CollectTyreData(ACarAvatar* carAvatar, CarData& carData);
    
    // Private constructor to prevent instantiation
    CarDataManager() = default;
};
