#include "ApplicationCore.hpp"
#include "LoggingSystem.hpp"
#include "GameDataAccess.hpp"
#include "CarDataManager.hpp"
#include "DataExport.hpp"
#include "Weather.hpp"
#include "WidgetDebugger.hpp"
#include "SectorTiming.hpp"
#include <vector>

// Static member definitions
bool ApplicationCore::s_initialized = false;

bool ApplicationCore::Initialize()
{
  if (s_initialized) {
    return true;
  }

  // Initialize logging system first
  if (!LoggingSystem::Initialize()) {
    return false;
  }

  // Initialize game data access
  if (!GameDataAccess::Initialize()) {
    LoggingSystem::Shutdown();
    return false;
  }

  // Initialize feature modules
  if (!InitializeModules()) {
    GameDataAccess::Initialize(); // No explicit shutdown needed
    LoggingSystem::Shutdown();
    return false;
  }

  s_initialized = true;
  return true;
}

void ApplicationCore::Shutdown()
{
  if (!s_initialized) {
    return;
  }

  ShutdownModules();
  LoggingSystem::Shutdown();
  s_initialized = false;
}

DWORD ApplicationCore::Run()
{
  if (!Initialize()) {
    return 1;
  }

  bool success = ExecuteMainLogic();

  Shutdown();

  return success ? 0 : 1;
}

bool ApplicationCore::IsInitialized()
{
  return s_initialized;
}

bool ApplicationCore::InitializeModules()
{
  try {
    // Initialize weather manager
    WeatherManager::Initialize();
    // Other modules don't require explicit initialization
    return true;
  } catch (...) {
    return false;
  }
}

void ApplicationCore::ShutdownModules()
{
  // Currently no explicit shutdown needed for feature modules
  // Weather manager and others handle their own cleanup
}

bool ApplicationCore::ExecuteMainLogic()
{
  try {
    if (!GameDataAccess::IsReady()) {
      return false;
    }

    auto &logStream = LoggingSystem::GetLogStream();

    // Log weather data using the Weather feature
    AAcRaceGameMode *raceGameMode = GameDataAccess::GetRaceGameMode();
    if (raceGameMode) {
      WeatherManager::LogWeatherData(raceGameMode, logStream);
    }

    // Use modular widget debugging system
    UWorld *world = GameDataAccess::GetWorld();
    if (world) {
      WidgetDebugger::AnalyzeAllWidgets(world, logStream);
      WidgetDebugger::FindTimingWidgets(world, logStream);
      SectorTiming::DebugTimingWidgets(world, logStream);
    }

    // Collect and export all car data
    std::vector<CarData> carDataList;
    int32 carCount = CarDataManager::CollectAllCarData(carDataList);

    if (carCount > 0) {
      DataExport::ExportAllCarData(carDataList, logStream);
    }

    // Flush the log
    LoggingSystem::Flush();

    return true;
  } catch (...) {
    return false;
  }
}
