#include "GameDataAccess.hpp"
#include "../SDK/UMG_classes.hpp"
#include "../SDK/WDG_HudMfdRealtimeItemBase_classes.hpp"

// Static member definitions
UEngine* GameDataAccess::s_engine = nullptr;
UWorld* GameDataAccess::s_world = nullptr;
ACarAvatar* GameDataAccess::s_playerCar = nullptr;
bool GameDataAccess::s_initialized = false;

bool GameDataAccess::Initialize() {
    try {
        RefreshGameObjects();
        s_initialized = (s_engine != nullptr && s_world != nullptr);
        return s_initialized;
    }
    catch (...) {
        s_initialized = false;
        return false;
    }
}

UEngine* GameDataAccess::GetEngine() {
    if (!s_initialized) {
        RefreshGameObjects();
    }
    return s_engine;
}

UWorld* GameDataAccess::GetWorld() {
    if (!s_initialized) {
        RefreshGameObjects();
    }
    return s_world;
}

ACarAvatar* GameDataAccess::GetPlayerCar() {
    if (!s_initialized || !s_world) {
        RefreshGameObjects();
    }
    
    if (!s_playerCar && s_world) {
        UBP_ACCUtils_C::ACC_GetPlayerCarAvatar(s_world, &s_playerCar);
    }
    
    return s_playerCar;
}

AGameModeBase* GameDataAccess::GetGameMode() {
    UWorld* world = GetWorld();
    if (!world) return nullptr;
    
    return UGameplayStatics::GetGameMode(world);
}

AAcRaceGameMode* GameDataAccess::GetRaceGameMode() {
    AGameModeBase* gameMode = GetGameMode();
    if (!gameMode) return nullptr;
    
    return static_cast<AAcRaceGameMode*>(gameMode);
}

int32 GameDataAccess::GetAllCars(TArray<AActor*>& outCars) {
    UWorld* world = GetWorld();
    if (!world) return 0;
    
    UGameplayStatics::GetAllActorsOfClass(world, ACarAvatar::StaticClass(), &outCars);
    return outCars.Num();
}

int32 GameDataAccess::GetAllRealtimeWidgets(TArray<UUserWidget*>& outWidgets) {
    UWorld* world = GetWorld();
    if (!world) return 0;
    
    UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &outWidgets, UWDG_HudMfdRealtimeItemBase_C::StaticClass(), false);
    return outWidgets.Num();
}

bool GameDataAccess::IsReady() {
    return s_initialized && s_engine && s_world;
}

void GameDataAccess::RefreshGameObjects() {
    s_engine = UEngine::GetEngine();
    s_world = UWorld::GetWorld();
    s_playerCar = nullptr; // Will be refreshed on next GetPlayerCar() call
}
