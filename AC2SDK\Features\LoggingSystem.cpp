#include "LoggingSystem.hpp"
#include <filesystem>
#include <iostream>

// Static member definitions
std::unique_ptr<std::ofstream> LoggingSystem::s_logFile = nullptr;
bool LoggingSystem::s_initialized = false;

bool LoggingSystem::Initialize(const std::string& logPath) {
    if (s_initialized) {
        return true; // Already initialized
    }
    
    try {
        // Create logs directory if it doesn't exist
        std::filesystem::path logFilePath(logPath);
        std::filesystem::create_directories(logFilePath.parent_path());
        
        // Create and open log file
        s_logFile = std::make_unique<std::ofstream>(logPath, std::ios::out);
        if (!s_logFile->is_open()) {
            s_logFile.reset();
            return false;
        }
        
        s_initialized = true;
        
        // Write initial header
        WriteHeader("AC2 ESSENTIAL RACE DATA");
        
        return true;
    }
    catch (const std::exception& e) {
        s_logFile.reset();
        s_initialized = false;
        return false;
    }
}

void LoggingSystem::Shutdown() {
    if (s_logFile && s_logFile->is_open()) {
        s_logFile->flush();
        s_logFile->close();
    }
    s_logFile.reset();
    s_initialized = false;
}

std::ofstream& LoggingSystem::GetLogStream() {
    if (!s_initialized || !s_logFile) {
        throw std::runtime_error("LoggingSystem not initialized");
    }
    return *s_logFile;
}

bool LoggingSystem::IsInitialized() {
    return s_initialized && s_logFile && s_logFile->is_open();
}

void LoggingSystem::WriteHeader(const std::string& title) {
    if (!IsInitialized()) return;
    
    *s_logFile << std::endl;
    *s_logFile << "=====================================" << std::endl;
    *s_logFile << "====== " << title << " ======" << std::endl;
    *s_logFile << "=====================================" << std::endl;
}

void LoggingSystem::WriteSection(const std::string& sectionName) {
    if (!IsInitialized()) return;
    
    *s_logFile << std::endl;
    *s_logFile << "====== " << sectionName << " ======" << std::endl;
}

void LoggingSystem::Flush() {
    if (IsInitialized()) {
        s_logFile->flush();
    }
}
