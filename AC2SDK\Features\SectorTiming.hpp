#pragma once

#include "../SDK.hpp"
#include "../SDK/Basic.hpp"
#include "../SDK/Engine_classes.hpp"
#include "../SDK/WDG_TimeTable_classes.hpp"
#include "../SDK/WDG_TimeTableItem_classes.hpp"
#include "../SDK/WDG_SectorTimeItem_classes.hpp"
#include "../SDK/WDG_LaptimeInfo0New_classes.hpp"
#include <string>
#include <fstream>

using namespace SDK;

/**
 * Data structure to hold sector timing information for a car
 */
struct SectorTimingData {
    std::string sectorOne = "+99:99:99,999";
    std::string sectorTwo = "+99:99:99,999";
    std::string sectorThree = "+99:99:99,999";
    bool isSectorOneBest = false;
    bool isSectorTwoBest = false;
    bool isSectorThreeBest = false;
    std::string lapTime = "N/A";
    std::string deltaToFirst = "+99:99:99,999";
    std::string deltaToFastest = "+99:99:99,999";
    int32 position = 0;
    int32 lapCount = 0;
    int32 mandatoryPitStopsLeft = 255;
};

/**
 * Sector Timing System - Extracts sector timing data for all cars
 * Handles different timing widget types and provides comprehensive timing information
 */
class SectorTiming {
public:
    /**
     * Get sector timing data for a specific car
     * @param world The game world
     * @param carNumber The car's race number
     * @return SectorTimingData structure with timing information
     */
    static SectorTimingData GetSectorTimingForCar(UWorld* world, int32 carNumber);
    
    /**
     * Get sector timing data for all cars
     * @param world The game world
     * @param logFile Output stream for logging
     * @return Array of SectorTimingData for all cars
     */
    static TArray<SectorTimingData> GetAllCarsSectorTiming(UWorld* world, std::ofstream& logFile);
    
    /**
     * Debug timing widgets and their contents
     * @param world The game world
     * @param logFile Output stream for logging
     */
    static void DebugTimingWidgets(UWorld* world, std::ofstream& logFile);
    
    /**
     * Extract timing data from TimeTable widgets
     * @param world The game world
     * @param carNumber The car's race number
     * @return SectorTimingData from TimeTable widgets
     */
    static SectorTimingData ExtractFromTimeTable(UWorld* world, int32 carNumber);
    
    /**
     * Extract timing data from TV Session TimeTable widgets
     * @param world The game world
     * @param carNumber The car's race number
     * @return SectorTimingData from TV TimeTable widgets
     */
    static SectorTimingData ExtractFromTVTimeTable(UWorld* world, int32 carNumber);
    
    /**
     * Extract timing data from LaptimeInfo widgets (player-specific)
     * @param world The game world
     * @return SectorTimingData from LaptimeInfo widgets
     */
    static SectorTimingData ExtractFromLaptimeInfo(UWorld* world);
    
    /**
     * Extract timing data from FinalHotLapResultItems widgets
     * @param world The game world
     * @param carNumber The car's race number
     * @return SectorTimingData from HotLap widgets
     */
    static SectorTimingData ExtractFromHotLapResults(UWorld* world, int32 carNumber);

private:
    /**
     * Helper function to safely extract text from a text widget
     * @param textWidget The text widget to extract from
     * @return Extracted text as string
     */
    static std::string SafeExtractText(UTextBlock* textWidget);
    
    /**
     * Helper function to match car avatars with timing table positions
     * @param world The game world
     * @param tablePosition Position in timing table
     * @return Car number at that position, or -1 if not found
     */
    static int32 GetCarNumberAtPosition(UWorld* world, int32 tablePosition);
};
