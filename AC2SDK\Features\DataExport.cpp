#include "DataExport.hpp"
#include "LoggingSystem.hpp"
#include <iomanip>
#include <sstream>

void DataExport::ExportAllCarData(const std::vector<CarData>& carDataList, std::ofstream& logStream) {
    LoggingSystem::WriteSection("ALL CARS ESSENTIAL DATA");
    
    for (size_t i = 0; i < carDataList.size(); i++) {
        ExportCarData(carDataList[i], static_cast<int32>(i + 1), logStream);
        
        // Export player car specific data if this is the player car
        if (carDataList[i].hasTyreData) {
            ExportPlayerCarData(carDataList[i], logStream);
        }
    }
}

void DataExport::ExportCarData(const CarData& carData, int32 carIndex, std::ofstream& logStream) {
    logStream << "=== CAR " << carIndex << " ===" << std::endl;
    logStream << "Car Number: " << carData.carInfo.RaceNumber << std::endl;
    logStream << "Team Name: " << carData.carInfo.TeamName.ToString() << std::endl;
    
    // Export penalty data
    ExportPenaltyData(carData.penaltyData, logStream);
    
    // Export sector timing data
    ExportSectorTimingData(carData.sectorData, carData.carInfo.RaceNumber, logStream);
    
    // Export pitstop data
    logStream << "Remaining Mandatory Pitstops: " << (int)carData.pitstopMFD.missingMandatoryPitstops << std::endl;
}

void DataExport::ExportPlayerCarData(const CarData& carData, std::ofstream& logStream) {
    if (!carData.hasTyreData) {
        return;
    }
    
    ExportTyreData(carData, logStream);
}

void DataExport::ExportPenaltyData(const PenaltyData& penaltyData, std::ofstream& logStream) {
    logStream << "Penalty Type: " << PenaltyManager::GetPenaltyTypeName(penaltyData.penaltyType) << std::endl;
    logStream << "Penalty Weight: " << (int)penaltyData.penaltyWeight << " seconds" << std::endl;
}

void DataExport::ExportSectorTimingData(const SectorTimingData& sectorData, int32 carNumber, std::ofstream& logStream) {
    logStream << "=== TIMING DATA FOR CAR " << carNumber << " ===" << std::endl;
    logStream << "SectorOne: " << sectorData.sectorOne << (sectorData.isSectorOneBest ? " (BEST)" : "") << std::endl;
    logStream << "SectorTwo: " << sectorData.sectorTwo << (sectorData.isSectorTwoBest ? " (BEST)" : "") << std::endl;
    logStream << "SectorThree: " << sectorData.sectorThree << (sectorData.isSectorThreeBest ? " (BEST)" : "") << std::endl;
    logStream << "Lap Time: " << sectorData.lapTime << std::endl;
    logStream << "Delta to First: " << sectorData.deltaToFirst << std::endl;
    logStream << "Position: " << sectorData.position << std::endl;
    logStream << "Lap Count: " << sectorData.lapCount << std::endl;
}

void DataExport::ExportTyreData(const CarData& carData, std::ofstream& logStream) {
    logStream << "=== PLAYER CAR TYRE DATA ===" << std::endl;
    logStream << "Tyre Sets Available: " << carData.tyreSets.Num() << std::endl;
    logStream << "Current Tyre Set Index: " << carData.currentTyreSetIndex << std::endl;
    
    logStream << "Tyre Status (Core Temp/Pressure):" << std::endl;
    for (int32 wheelIndex = 0; wheelIndex < 4; wheelIndex++) {
        const auto& tyreStatus = carData.tyreStatus[wheelIndex];
        logStream << "  " << GetWheelName(wheelIndex) << ": " 
                  << tyreStatus.coreTemp << "C / " 
                  << tyreStatus.pressure << " PSI" << std::endl;
    }
}

std::string DataExport::FormatTime(float timeValue) {
    std::ostringstream oss;
    oss << std::fixed << std::setprecision(3) << timeValue;
    return oss.str();
}

const char* DataExport::GetWheelName(int32 wheelIndex) {
    static const char* wheelNames[] = {"FL", "FR", "RL", "RR"};
    if (wheelIndex >= 0 && wheelIndex < 4) {
        return wheelNames[wheelIndex];
    }
    return "UNKNOWN";
}
