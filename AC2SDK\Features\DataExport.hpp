#pragma once

#include "CarDataManager.hpp"
#include <vector>
#include <fstream>

/**
 * @brief Handles formatting and exporting collected game data
 * 
 * Provides clean interfaces for outputting car data, weather information,
 * and other game state to various formats (currently log files).
 */
class DataExport {
public:
    /**
     * @brief Export all car data to the log stream
     * @param carDataList Vector of car data to export
     * @param logStream Output stream to write to
     */
    static void ExportAllCarData(const std::vector<CarData>& carDataList, std::ofstream& logStream);
    
    /**
     * @brief Export data for a single car
     * @param carData The car data to export
     * @param carIndex The car index (for display purposes)
     * @param logStream Output stream to write to
     */
    static void ExportCarData(const CarData& carData, int32 carIndex, std::ofstream& logStream);
    
    /**
     * @brief Export player car specific data (including tyre information)
     * @param carData The player car data to export
     * @param logStream Output stream to write to
     */
    static void ExportPlayerCarData(const CarData& carData, std::ofstream& logStream);
    
    /**
     * @brief Export penalty information for a car
     * @param penaltyData The penalty data to export
     * @param logStream Output stream to write to
     */
    static void ExportPenaltyData(const PenaltyData& penaltyData, std::ofstream& logStream);
    
    /**
     * @brief Export sector timing information for a car
     * @param sectorData The sector timing data to export
     * @param carNumber The car number for display
     * @param logStream Output stream to write to
     */
    static void ExportSectorTimingData(const SectorTimingData& sectorData, int32 carNumber, std::ofstream& logStream);
    
    /**
     * @brief Export tyre data for the player car
     * @param carData The car data containing tyre information
     * @param logStream Output stream to write to
     */
    static void ExportTyreData(const CarData& carData, std::ofstream& logStream);

private:
    /**
     * @brief Format a time value for display
     * @param timeValue The time value to format
     * @return Formatted time string
     */
    static std::string FormatTime(float timeValue);
    
    /**
     * @brief Get wheel name by index
     * @param wheelIndex The wheel index (0-3)
     * @return Wheel name string
     */
    static const char* GetWheelName(int32 wheelIndex);
    
    // Private constructor to prevent instantiation
    DataExport() = default;
};
