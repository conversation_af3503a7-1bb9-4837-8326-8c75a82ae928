#pragma once

#include <fstream>
#include <string>
#include <memory>

/**
 * @brief Centralized logging system for the AC2 SDK
 * 
 * Provides RAII-based file logging with automatic directory creation
 * and proper resource management.
 */
class LoggingSystem {
public:
    /**
     * @brief Initialize the logging system
     * @param logPath Path to the log file
     * @return true if initialization successful, false otherwise
     */
    static bool Initialize(const std::string& logPath = "Z:\\Work\\UpWork\\AC2\\AC2SDK\\logs\\dll.log");
    
    /**
     * @brief Shutdown the logging system
     */
    static void Shutdown();
    
    /**
     * @brief Get the current log file stream
     * @return Reference to the log file stream
     */
    static std::ofstream& GetLogStream();
    
    /**
     * @brief Check if logging system is initialized
     * @return true if initialized, false otherwise
     */
    static bool IsInitialized();
    
    /**
     * @brief Write a header to the log file
     * @param title The header title
     */
    static void WriteHeader(const std::string& title);
    
    /**
     * @brief Write a section separator to the log file
     * @param sectionName The section name
     */
    static void WriteSection(const std::string& sectionName);
    
    /**
     * @brief Flush the log file
     */
    static void Flush();

private:
    static std::unique_ptr<std::ofstream> s_logFile;
    static bool s_initialized;
    
    // Private constructor to prevent instantiation
    LoggingSystem() = default;
};
