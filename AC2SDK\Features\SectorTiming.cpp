#include "SectorTiming.hpp"
#include "../SDK/UMG_classes.hpp"

SectorTimingData SectorTiming::GetSectorTimingForCar(UWorld *world, int32 carNumber)
{
  SectorTimingData result;

  if (!world) {
    return result;
  }

  // Try different extraction methods in order of preference

  // 1. Try TimeTable widgets first (most comprehensive)
  result = ExtractFromTimeTable(world, carNumber);
  if (result.sectorOne != "+99:99:99,999") {
    return result;
  }

  // 2. Try TV Session TimeTable widgets
  result = ExtractFromTVTimeTable(world, carNumber);
  if (result.sectorOne != "+99:99:99,999") {
    return result;
  }

  // 3. Try HotLap results widgets
  result = ExtractFromHotLapResults(world, carNumber);
  if (result.sectorOne != "+99:99:99,999") {
    return result;
  }

  // 4. For player car, try LaptimeInfo widgets
  result = ExtractFromLaptimeInfo(world);

  return result;
}

TArray<SectorTimingData> SectorTiming::GetAllCarsSectorTiming(UWorld *world, std::ofstream &logFile)
{
  TArray<SectorTimingData> allTimingData;

  if (!world) {
    logFile << "ERROR: World is null in GetAllCarsSectorTiming" << std::endl;
    return allTimingData;
  }

  // Get all car avatars
  TArray<AActor *> foundCars;
  UGameplayStatics::GetAllActorsOfClass(world, ACarAvatar::StaticClass(), &foundCars);

  logFile << "Getting sector timing for " << foundCars.Num() << " cars" << std::endl;

  for (AActor *actor : foundCars) {
    ACarAvatar *carAvatar = static_cast<ACarAvatar *>(actor);
    if (carAvatar) {
      FCarInfo carInfo = carAvatar->getCarInfoUI();
      SectorTimingData timingData = GetSectorTimingForCar(world, carInfo.RaceNumber);
      allTimingData.Add(timingData);
    }
  }

  return allTimingData;
}

void SectorTiming::DebugTimingWidgets(UWorld *world, std::ofstream &logFile)
{
  if (!world) {
    logFile << "ERROR: World is null in DebugTimingWidgets" << std::endl;
    return;
  }

  logFile << "\n====== TIMING WIDGETS DEBUG ======" << std::endl;

  // Debug TimeTable widgets
  TArray<UUserWidget *> timeTableWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &timeTableWidgets, UWDG_TimeTable_C::StaticClass(), false);
  logFile << "Found " << timeTableWidgets.Num() << " TimeTable widgets" << std::endl;

  // Debug TV Session TimeTable widgets
  TArray<UUserWidget *> tvTimeTableWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &tvTimeTableWidgets, UWDG_TVSessionTimeTable_C::StaticClass(), false);
  logFile << "Found " << tvTimeTableWidgets.Num() << " TV Session TimeTable widgets" << std::endl;

  // Debug LaptimeInfo widgets
  TArray<UUserWidget *> laptimeWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &laptimeWidgets, UWDG_LaptimeInfo0New_C::StaticClass(), false);
  logFile << "Found " << laptimeWidgets.Num() << " LaptimeInfo widgets" << std::endl;

  // Debug Sector Time widgets
  TArray<UUserWidget *> sectorWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &sectorWidgets, UWDG_SectorTimeItem_C::StaticClass(), false);
  logFile << "Found " << sectorWidgets.Num() << " Sector Time widgets" << std::endl;

  // Debug FinalHotLapResultItems widgets
  TArray<UUserWidget *> finalHotLapWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &finalHotLapWidgets, UFinalHotLapResultItems::StaticClass(), false);
  logFile << "Found " << finalHotLapWidgets.Num() << " FinalHotLapResultItems widgets" << std::endl;
}

SectorTimingData SectorTiming::ExtractFromTimeTable(UWorld *world, int32 carNumber)
{
  SectorTimingData result;

  if (!world) {
    return result;
  }

  TArray<UUserWidget *> timeTableWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &timeTableWidgets, UWDG_TimeTable_C::StaticClass(), false);

  for (UUserWidget *widget : timeTableWidgets) {
    UWDG_TimeTable_C *timeTable = static_cast<UWDG_TimeTable_C *>(widget);
    if (!timeTable || !timeTable->IsVisible()) {
      continue;
    }

    if (timeTable->ScrollTimes) {
      TArray<UWidget *> children = timeTable->ScrollTimes->GetAllChildren();

      for (int32 i = 0; i < children.Num(); i++) {
        UWDG_TimeTableItem_C *timeTableItem = static_cast<UWDG_TimeTableItem_C *>(children[i]);
        if (!timeTableItem) {
          continue;
        }

        // Match by position in timing table with car avatars
        int32 carAtPosition = GetCarNumberAtPosition(world, i);
        if (carAtPosition == carNumber) {
          // Extract timing data
          if (timeTableItem->SectorOne && timeTableItem->SectorOne->txtValue) {
            result.sectorOne = SafeExtractText(timeTableItem->SectorOne->txtValue);
          }

          if (timeTableItem->SectorTwo && timeTableItem->SectorTwo->txtValue) {
            result.sectorTwo = SafeExtractText(timeTableItem->SectorTwo->txtValue);
          }

          if (timeTableItem->SectorThree && timeTableItem->SectorThree->txtValue) {
            result.sectorThree = SafeExtractText(timeTableItem->SectorThree->txtValue);
          }

          if (timeTableItem->LapTime && timeTableItem->LapTime->txtValue) {
            result.lapTime = SafeExtractText(timeTableItem->LapTime->txtValue);
          }

          if (timeTableItem->DeltaTime && timeTableItem->DeltaTime->txtValue) {
            result.deltaToFirst = SafeExtractText(timeTableItem->DeltaTime->txtValue);
          }

          result.position = i + 1;

          if (timeTableItem->txtPos) {
            std::string posStr = SafeExtractText(timeTableItem->txtPos);
            if (!posStr.empty()) {
              try {
                int pos = std::stoi(posStr);
                if (pos > 0) {
                  result.position = pos;
                }
              } catch (...) {
                // Keep fallback position
              }
            }
          }

          if (timeTableItem->txtLapCount) {
            std::string lapCountStr = SafeExtractText(timeTableItem->txtLapCount);
            if (!lapCountStr.empty()) {
              try {
                result.lapCount = std::stoi(lapCountStr);
              } catch (...) {
                // Keep default value
              }
            }
          }

          return result;
        }
      }
    }
  }

  return result;
}

SectorTimingData SectorTiming::ExtractFromTVTimeTable(UWorld *world, int32 carNumber)
{
  SectorTimingData result;

  // Similar implementation to ExtractFromTimeTable but for TV widgets
  // This is a placeholder - would need the actual TV TimeTable widget structure

  return result;
}

SectorTimingData SectorTiming::ExtractFromLaptimeInfo(UWorld *world)
{
  SectorTimingData result;

  if (!world) {
    return result;
  }

  TArray<UUserWidget *> laptimeWidgets;
  UWidgetBlueprintLibrary::GetAllWidgetsOfClass(world, &laptimeWidgets, UWDG_LaptimeInfo0New_C::StaticClass(), false);

  for (UUserWidget *widget : laptimeWidgets) {
    UWDG_LaptimeInfo0New_C *laptimeInfo = static_cast<UWDG_LaptimeInfo0New_C *>(widget);
    if (laptimeInfo && laptimeInfo->IsVisible()) {
      // Access the base class to get sector timing text blocks
      ULaptimeInfo01Widget *baseLaptimeInfo = static_cast<ULaptimeInfo01Widget *>(laptimeInfo);
      if (baseLaptimeInfo) {
        // Extract sector times from the base class text blocks
        if (baseLaptimeInfo->txtCurrentSplit1) {
          result.sectorOne = SafeExtractText(baseLaptimeInfo->txtCurrentSplit1);
        }

        if (baseLaptimeInfo->txtCurrentSplit2) {
          result.sectorTwo = SafeExtractText(baseLaptimeInfo->txtCurrentSplit2);
        }

        if (baseLaptimeInfo->txtCurrentSplit3) {
          result.sectorThree = SafeExtractText(baseLaptimeInfo->txtCurrentSplit3);
        }
      }

      break; // Only need first visible widget
    }
  }

  return result;
}

SectorTimingData SectorTiming::ExtractFromHotLapResults(UWorld *world, int32 carNumber)
{
  SectorTimingData result;

  // Implementation for FinalHotLapResultItems widgets
  // This would extract from qualifying/hotlap result screens

  return result;
}

std::string SectorTiming::SafeExtractText(UTextBlock *textWidget)
{
  if (!textWidget) {
    return "";
  }

  FText text = textWidget->GetText();
  if (text.TextData && text.TextData->TextSource.ToString().length() > 0) {
    return text.ToString();
  }

  return "";
}

int32 SectorTiming::GetCarNumberAtPosition(UWorld *world, int32 tablePosition)
{
  if (!world) {
    return -1;
  }

  TArray<AActor *> foundCars;
  UGameplayStatics::GetAllActorsOfClass(world, ACarAvatar::StaticClass(), &foundCars);

  if (tablePosition < foundCars.Num()) {
    ACarAvatar *carAvatar = static_cast<ACarAvatar *>(foundCars[tablePosition]);
    if (carAvatar) {
      FCarInfo carInfo = carAvatar->getCarInfoUI();
      return carInfo.RaceNumber;
    }
  }

  return -1;
}
